using System;
using QuantConnect.Benchmarks;
using QuantConnect.Orders.Fees;
using QuantConnect.Securities;
using QuantConnect.Securities.CryptoFuture;

namespace QuantConnect.Brokerages
{
    public class MexcFuturesBrokerageModel : MexcBrokerageModel
    {
        public MexcFuturesBrokerageModel(AccountType accountType) : base(accountType)
        {
            if (accountType == AccountType.Cash)
            {
                throw new InvalidOperationException($"{SecurityType.CryptoFuture} can only be traded using a {AccountType.Margin} account type");
            }
        }

        public override IBenchmark GetBenchmark(SecurityManager securities)
        {
            var symbol = Symbol.Create("BTCUSDT", SecurityType.CryptoFuture, MarketName);
            return SecurityBenchmark.CreateInstance(securities, symbol);
        }

        public override IFeeModel GetFeeModel(Security security)
        {
            return new MexcFuturesFeeModel();
        }
    }
}
