namespace QuantConnect.Orders.Fees
{
    public class MexcFuturesFeeModel : MexcFeeModel
    {
        public const decimal MakerTier1USDTFee = 0.0002m;
        public const decimal TakerTier1USDTFee = 0.0004m;

        private readonly decimal _makerUsdtFee;
        private readonly decimal _takerUsdtFee;

        public MexcFuturesFeeModel(decimal mUsdtFee = MakerTier1USDTFee, decimal tUsdtFee = TakerTier1USDTFee)
            : base(-1, -1)
        {
            _makerUsdtFee = mUsdtFee;
            _takerUsdtFee = tUsdtFee;
        }

        protected override decimal GetFee(Order order)
        {
            CurrencyPairUtil.DecomposeCurrencyPair(order.Symbol, out var _, out var quoteCurrency);
            var makerFee = _makerUsdtFee;
            var takerFee = _takerUsdtFee;

            return GetFee(order, makerFee, takerFee);
        }
    }
}