using System;
using System.Collections.Generic;
using QuantConnect.Benchmarks;
using QuantConnect.Orders.Fees;
using QuantConnect.Securities;

namespace QuantConnect.Brokerages
{
    public class MexcBrokerageModel : DefaultBrokerageModel
    {
        private const decimal _defaultLeverage = 3;
        private const decimal _defaultFutureLeverage = 25;

        protected virtual string BaseApiEndpoint => "https://api.mexc.com/api/v3";

        protected virtual string MarketName => Market.MEXC;

        public override IReadOnlyDictionary<SecurityType, string> DefaultMarkets { get; } = GetDefaultMarkets(Market.MEXC);

        public MexcBrokerageModel(AccountType accountType = AccountType.Cash) : base(accountType)
        {
        }

        public override decimal GetLeverage(Security security)
        {
            if (AccountType == AccountType.Cash || security.IsInternalFeed() || security.Type == SecurityType.Base)
            {
                return 1m;
            }

            return security.Symbol.SecurityType == SecurityType.CryptoFuture ? _defaultFutureLeverage : _defaultLeverage;
        }

        public override IBenchmark GetBenchmark(SecurityManager securities)
        {
            var symbol = Symbol.Create("BTCUSDT", SecurityType.Crypto, MarketName);
            return SecurityBenchmark.CreateInstance(securities, symbol);
        }

        public override IFeeModel GetFeeModel(Security security)
        {
            return security.Type switch
            {
                SecurityType.Crypto => new MexcFeeModel(),
                SecurityType.CryptoFuture => new MexcFuturesFeeModel(),
                SecurityType.Base => base.GetFeeModel(security),
                _ => throw new ArgumentOutOfRangeException(nameof(security), security, $"Not supported security type {security.Type}")
            };
        }

        public override bool CanSubmitOrder(Security security, Order order, out BrokerageMessageEvent message)
        {
            message = null;

            if (security.Type != SecurityType.Base && security.Type != SecurityType.Crypto && security.Type != SecurityType.CryptoFuture)
            {
                message = new BrokerageMessageEvent(BrokerageMessageType.Warning, "NotSupported",
                    Messages.DefaultBrokerageModel.UnsupportedSecurityType(this, security));

                return false;
            }
            return base.CanSubmitOrder(security, order, out message);

            bool IsOrderSizeLargeEnough(decimal price) =>
                !security.SymbolProperties.MinimumOrderSize.HasValue || order.AbsoluteQuantity * price > security.SymbolProperties.MinimumOrderSize;
        }
    }
}
