﻿using System;
using QuantConnect.Data;
using QuantConnect.Util;
using QuantConnect.Orders;
using QuantConnect.Packets;
using QuantConnect.Interfaces;
using QuantConnect.Securities;
using System.Collections.Generic;
namespace QuantConnect.Brokerages.Mexc
{
    [BrokerageFactory(typeof(MexcBrokerageFactory))]
    public class MexcBrokerage : Brokerage, IDataQueueHandler, IDataQueueUniverseProvider
    {
        private readonly IDataAggregator _aggregator;
        private readonly EventBasedDataQueueHandlerSubscriptionManager _subscriptionManager;
        public override bool IsConnected { get; }
        public MexcBrokerage()
            : this(Composer.Instance.GetPart<IDataAggregator>())
        {
        }
        public MexcBrokerage(IDataAggregator aggregator) : base("MexcBrokerage")
        {
            _aggregator = aggregator;
            _subscriptionManager = new EventBasedDataQueueHandlerSubscriptionManager();
            _subscriptionManager.SubscribeImpl += (s, t) => Subscribe(s);
            _subscriptionManager.UnsubscribeImpl += (s, t) => Unsubscribe(s);
        }
        #region IDataQueueHandler
        public IEnumerator<BaseData> Subscribe(SubscriptionDataConfig dataConfig, EventHandler newDataAvailableHandler)
        {
            if (!CanSubscribe(dataConfig.Symbol))
            {
                return null;
            }
            var enumerator = _aggregator.Add(dataConfig, newDataAvailableHandler);
            _subscriptionManager.Subscribe(dataConfig);
            return enumerator;
        }
        public void Unsubscribe(SubscriptionDataConfig dataConfig)
        {
            _subscriptionManager.Unsubscribe(dataConfig);
            _aggregator.Remove(dataConfig);
        }
        public void SetJob(LiveNodePacket job)
        {
            throw new NotImplementedException();
        }
        #endregion
        #region Brokerage
        public override List<Order> GetOpenOrders()
        {
            throw new NotImplementedException();
        }
        public override List<Holding> GetAccountHoldings()
        {
            throw new NotImplementedException();
        }
        public override List<CashAmount> GetCashBalance()
        {
            throw new NotImplementedException();
        }
        public override bool PlaceOrder(Order order)
        {
            throw new NotImplementedException();
        }
        public override bool UpdateOrder(Order order)
        {
            throw new NotImplementedException();
        }
        public override bool CancelOrder(Order order)
        {
            throw new NotImplementedException();
        }
        public override void Connect()
        {
            throw new NotImplementedException();
        }
        public override void Disconnect()
        {
            throw new NotImplementedException();
        }
        #endregion
        #region IDataQueueUniverseProvider
        public IEnumerable<Symbol> LookupSymbols(Symbol symbol, bool includeExpired, string securityCurrency = null)
        {
            throw new NotImplementedException();
        }
        public bool CanPerformSelection()
        {
            throw new NotImplementedException();
        }
        #endregion
        private bool CanSubscribe(Symbol symbol)
        {
            if (symbol.Value.IndexOfInvariant("universe", true) != -1 || symbol.IsCanonical())
            {
                return false;
            }
            throw new NotImplementedException();
        }
        private bool Subscribe(IEnumerable<Symbol> symbols)
        {
            throw new NotImplementedException();
        }
        private bool Unsubscribe(IEnumerable<Symbol> symbols)
        {
            throw new NotImplementedException();
        }
        public override IEnumerable<BaseData> GetHistory(Data.HistoryRequest request)
        {
            if (!CanSubscribe(request.Symbol))
            {
                return null;
            }
            throw new NotImplementedException();
        }
    }
}