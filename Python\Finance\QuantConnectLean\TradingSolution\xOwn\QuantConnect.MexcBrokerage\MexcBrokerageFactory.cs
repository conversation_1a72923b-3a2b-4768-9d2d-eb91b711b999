﻿using System;
using QuantConnect.Packets;
using QuantConnect.Brokerages;
using QuantConnect.Interfaces;
using QuantConnect.Securities;
using System.Collections.Generic;
namespace QuantConnect.Brokerages.Mexc
{
    public class MexcBrokerageFactory : BrokerageFactory
    {
        public override Dictionary<string, string> BrokerageData { get; }
        public MexcBrokerageFactory() : base(typeof(MexcBrokerage))
        {
        }
        public override IBrokerageModel GetBrokerageModel(IOrderProvider orderProvider)
        {
            throw new NotImplementedException();
        }
        public override IBrokerage CreateBrokerage(LiveNodePacket job, IAlgorithm algorithm)
        {
            throw new NotImplementedException();
        }
        public override void Dispose()
        {
            throw new NotImplementedException();
        }
    }
}